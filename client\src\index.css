@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme {
  --font-alibaba: "alibaba-sans", sans-serif;
}

body {
  background-color: #faf5f5;
}

/* Custom Select Styling */
.custom-select .ant-select-selector {
  border-radius: 8px !important;
  border: 1px solid #d1d5db !important;
  height: 48px !important;
  padding: 0 12px 0 40px !important;
  transition: all 0.2s ease !important;
}

.custom-select .ant-select-selector:hover {
  border-color: #f97316 !important;
}

.custom-select.ant-select-focused .ant-select-selector {
  border-color: #f97316 !important;
  box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.2) !important;
}

.custom-select .ant-select-selection-placeholder {
  color: #9ca3af !important;
  line-height: 46px !important;
}

.custom-select .ant-select-selection-item {
  line-height: 46px !important;
  color: #111827 !important;
}

/* Custom Checkbox Styling */
.ant-checkbox-wrapper {
  color: #6b7280 !important;
}

.ant-checkbox-checked .ant-checkbox-inner {
  background-color: #f97316 !important;
  border-color: #f97316 !important;
}

.ant-checkbox:hover .ant-checkbox-inner {
  border-color: #f97316 !important;
}

/* Form Item Styling */
.ant-form-item {
  margin-bottom: 0 !important;
}

.ant-form-item-explain-error {
  color: #ef4444 !important;
  font-size: 0.875rem !important;
  margin-top: 0.25rem !important;
}

/* Remove Ant Design default focus styles */
.ant-input:focus,
.ant-input-focused {
  border-color: transparent !important;
  box-shadow: none !important;
}

/* Custom scrollbar for select dropdown */
.ant-select-dropdown {
  border-radius: 8px !important;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
}

.ant-select-item-option-selected {
  background-color: #fed7aa !important;
  color: #ea580c !important;
}

.ant-select-item-option:hover {
  background-color: #ffedd5 !important;
}

/* Hide scrollbar for CategoriesSlider */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom transparent navbar styles - AGGRESSIVE OVERRIDE */
.navbar-transparent,
.navbar-transparent *,
.navbar-transparent::before,
.navbar-transparent::after {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
}

.navbar-trigger-transparent,
.navbar-trigger-transparent *,
.navbar-trigger-transparent::before,
.navbar-trigger-transparent::after {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
}

/* Override shadcn/ui navigation menu default styles */
[data-slot="navigation-menu-trigger"].navbar-trigger-transparent {
  background-color: transparent !important;
  background: transparent !important;
  --tw-bg-opacity: 0 !important;
}

/* Override CSS variables for transparent state */
.navbar-transparent {
  --background: transparent !important;
  --card: transparent !important;
  --popover: transparent !important;
  --secondary: transparent !important;
  --muted: transparent !important;
  --accent: transparent !important;
}

.navbar-trigger-transparent:hover {
  background-color: rgba(0, 0, 0, 0.1) !important;
  background: rgba(0, 0, 0, 0.1) !important;
  color: black !important;
}

.navbar-trigger-transparent:focus {
  background-color: transparent !important;
  background: transparent !important;
}

.navbar-trigger-transparent[data-state="open"] {
  background-color: rgba(0, 0, 0, 0.1) !important;
  background: rgba(0, 0, 0, 0.1) !important;
  color: black !important;
}

/* Force transparency on all possible background classes */
.navbar-transparent .bg-background,
.navbar-transparent .bg-card,
.navbar-transparent .bg-popover,
.navbar-transparent .bg-secondary,
.navbar-transparent .bg-muted,
.navbar-transparent .bg-accent {
  background-color: transparent !important;
  background: transparent !important;
}

/* Nuclear option - override everything with transparent */
.navbar-transparent,
.navbar-transparent > *,
.navbar-transparent > * > *,
.navbar-transparent > * > * > * {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
}

/* Specifically target navigation menu components */
.navbar-transparent [data-slot="navigation-menu"],
.navbar-transparent [data-slot="navigation-menu-list"],
.navbar-transparent [data-slot="navigation-menu-item"],
.navbar-transparent [data-slot="navigation-menu-trigger"] {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
}

/* Override the shadcn/ui navigationMenuTriggerStyle bg-background class */
.navbar-trigger-transparent.bg-background {
  background-color: transparent !important;
  background: transparent !important;
}

/* Override all possible shadcn/ui background classes on triggers */
.navbar-trigger-transparent.bg-accent,
.navbar-trigger-transparent.bg-secondary,
.navbar-trigger-transparent.bg-muted,
.navbar-trigger-transparent.bg-card,
.navbar-trigger-transparent.bg-popover {
  background-color: transparent !important;
  background: transparent !important;
}

/* Override hover states */
.navbar-trigger-transparent:hover.bg-accent,
.navbar-trigger-transparent:focus.bg-accent,
.navbar-trigger-transparent[data-state="open"].bg-accent {
  background-color: rgba(0, 0, 0, 0.1) !important;
  background: rgba(0, 0, 0, 0.1) !important;
  color: black !important;
}

/* Target the exact shadcn/ui navigationMenuTriggerStyle classes */
.navbar-trigger-transparent.group.inline-flex.bg-background,
.navbar-trigger-transparent.group.inline-flex.h-9.bg-background,
button.navbar-trigger-transparent.group.inline-flex.bg-background {
  background-color: transparent !important;
  background: transparent !important;
}

/* Override with maximum specificity */
button[data-slot="navigation-menu-trigger"].navbar-trigger-transparent.group.inline-flex.bg-background {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
}

/* Override NavigationMenuViewport background */
[data-slot="navigation-menu-viewport"] {
  background-color: white !important;
  background: white !important;
}

/* Override NavigationMenuContent background */
[data-slot="navigation-menu-content"] {
  background-color: white !important;
  background: white !important;
}

/* Ensure MainNavigationMenu container is fully transparent */
.navbar-transparent [data-slot="navigation-menu-list"] {
  background-color: transparent !important;
  background: transparent !important;
}

/* Override any remaining shadcn/ui backgrounds in MainNavigationMenu */
.navbar-transparent .bg-popover,
.navbar-transparent .bg-popover-foreground {
  background-color: transparent !important;
  background: transparent !important;
}

/* Navbar text hover styles - make text black on hover */
.navbar-text-element {
  color: white !important;
  text-shadow: none !important;
  transition: all 0.3s ease !important;
}

.navbar-text-element:hover {
  color: black !important;
  background-color: rgba(255, 255, 255, 0.9) !important;
  border-radius: 4px !important;
  padding: 2px 6px !important;
}

/* Remove all text shadows from navbar elements */
.navbar-no-shadow,
.navbar-no-shadow * {
  text-shadow: none !important;
  filter: none !important;
}

/* Force complete transparency on navbar containers */
.navbar-transparent,
.navbar-transparent > div,
.navbar-transparent > div > div {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

/* Override any white backgrounds that might appear */
.navbar-transparent .bg-white,
.navbar-transparent .bg-gray-50,
.navbar-transparent .bg-gray-100 {
  background-color: transparent !important;
  background: transparent !important;
}

/* Ensure navbar text is visible and has proper hover states */
.navbar-text-element span,
.navbar-text-element div {
  transition: all 0.3s ease !important;
}

/* Make sure icons and text change color together on hover */
.navbar-text-element:hover span,
.navbar-text-element:hover div,
.navbar-text-element:hover svg {
  color: black !important;
}